<template>
  <div>
    <SidebarProvider v-model:open="sidebarOpen">
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <div class="flex flex-1 flex-col gap-2 p-2 pt-0 h-0">
          <!-- 子路由内容区域 -->
          <div class="flex-1 rounded-xl bg-muted/50 overflow-hidden">
            <router-view />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>

<script setup lang="ts">
import AppSidebar from './components/AppSidebar.vue'
import Navbar from './components/Navbar.vue'

// 控制侧边栏是否展开的状态，设置为 false 表示默认不展开
const sidebarOpen = ref(false)
</script>

<style scoped></style>
