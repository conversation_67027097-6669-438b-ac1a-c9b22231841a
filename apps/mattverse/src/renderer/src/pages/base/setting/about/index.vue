<template>
  <div class="flex flex-col h-screen overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-shrink-0 px-6 py-4 border-b bg-background">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ t('settings.about.title') }}</h1>
          <p class="text-muted-foreground text-sm mt-1">{{ t('settings.about.subtitle') }}</p>
        </div>
      </div>
    </div>

    <!-- 关于内容 - 可滚动区域 -->
    <div class="flex-1 min-h-0 overflow-y-auto overflow-x-hidden">
      <div class="container mx-auto max-w-4xl p-8">
        <!-- 主要信息卡片 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 应用信息卡片 -->
          <Card
            class="bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300"
          >
            <CardContent class="p-8">
              <div class="flex flex-col items-center text-center space-y-6">
                <!-- Logo -->
                <div class="relative">
                  <div
                    class="absolute -inset-4 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full blur-xl"
                  ></div>
                  <div
                    class="relative w-24 h-24 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 p-4 shadow-2xl"
                  >
                    <img
                      :src="LOGO"
                      alt="MattVerse Logo"
                      class="w-full h-full object-contain filter brightness-0 invert"
                      @error="handleImageError"
                    />
                  </div>
                </div>

                <!-- 应用名称和版本 -->
                <div class="space-y-2">
                  <h2
                    class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                  >
                    {{ t('settings.about.app_name') }}
                  </h2>
                  <div class="flex items-center justify-center space-x-2">
                    <Badge variant="secondary" class="px-3 py-1"> v{{ appVersion }} </Badge>
                    <Badge variant="outline" class="px-3 py-1">
                      {{ t(`settings.about.${buildType.toLowerCase()}`) }}
                    </Badge>
                  </div>
                </div>

                <!-- 应用描述 -->
                <p class="text-muted-foreground text-center leading-relaxed max-w-sm">
                  {{ t('settings.about.app_description') }}
                </p>

                <!-- 操作按钮 -->
                <div class="flex space-x-3 pt-4">
                  <Button
                    variant="default"
                    size="sm"
                    @click="checkForUpdates"
                    class="flex items-center space-x-2"
                  >
                    <MattIcon name="Download" class="h-4 w-4" />
                    <span>{{ t('settings.about.check_updates') }}</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="openHomepage"
                    class="flex items-center space-x-2"
                  >
                    <MattIcon name="ExternalLink" class="h-4 w-4" />
                    <span>{{ t('settings.about.official_website') }}</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 系统信息卡片 -->
          <Card
            class="bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Monitor" class="h-5 w-5" />
                <span>{{ t('settings.about.system_info') }}</span>
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 gap-4">
                <div class="flex justify-between items-center py-2 border-b border-border/50">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.operating_system') }}</span>
                  <span class="text-sm font-medium">{{ systemInfo.platform }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-border/50">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.architecture') }}</span>
                  <span class="text-sm font-medium">{{ systemInfo.arch }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-border/50">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.nodejs_version') }}</span>
                  <span class="text-sm font-medium">{{ systemInfo.nodeVersion }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-border/50">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.electron_version') }}</span>
                  <span class="text-sm font-medium">{{ systemInfo.electronVersion }}</span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.chrome_version') }}</span>
                  <span class="text-sm font-medium">{{ systemInfo.chromeVersion }}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 开发团队和致谢 -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 开发团队 -->
          <Card
            class="bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Users" class="h-5 w-5" />
                <span>{{ t('settings.about.development_team') }}</span>
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <Avatar class="h-10 w-10">
                    <AvatarFallback
                      class="bg-gradient-to-br from-blue-500 to-purple-600 text-white"
                    >
                      M
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p class="text-sm font-medium">mattverse</p>
                    <p class="text-xs text-muted-foreground">{{ t('settings.about.chief_developer') }}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 开源许可 -->
          <Card
            class="bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="FileText" class="h-5 w-5" />
                <span>{{ t('settings.about.open_source_license') }}</span>
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">{{ t('settings.about.license_type') }}</span>
                  <Badge variant="secondary">MIT</Badge>
                </div>
                <p class="text-xs text-muted-foreground leading-relaxed">
                  {{ t('settings.about.license_description') }}
                </p>
                <Button size="sm" @click="viewLicense" class="w-full">{{ t('settings.about.view_license') }}</Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 联系方式和链接 -->
        <Card
          class="mt-8 bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300"
        >
          <CardHeader>
            <CardTitle class="flex items-center space-x-2">
              <MattIcon name="Link" class="h-5 w-5" />
              <span>{{ t('settings.about.related_links') }}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                variant="ghost"
                size="sm"
                @click="openLink('github')"
                class="flex flex-col items-center space-y-2 h-auto py-4 hover:bg-muted/50"
              >
                <MattIcon name="Github" class="h-6 w-6" />
                <span class="text-xs">{{ t('settings.about.github') }}</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="openLink('docs')"
                class="flex flex-col items-center space-y-2 h-auto py-4 hover:bg-muted/50"
              >
                <MattIcon name="BookOpen" class="h-6 w-6" />
                <span class="text-xs">{{ t('settings.about.documentation') }}</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="openLink('support')"
                class="flex flex-col items-center space-y-2 h-auto py-4 hover:bg-muted/50"
              >
                <MattIcon name="HelpCircle" class="h-6 w-6" />
                <span class="text-xs">{{ t('settings.about.support') }}</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="openLink('feedback')"
                class="flex flex-col items-center space-y-2 h-auto py-4 hover:bg-muted/50"
              >
                <MattIcon name="MessageSquare" class="h-6 w-6" />
                <span class="text-xs">{{ t('settings.about.feedback') }}</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { logger } from '@mattverse/shared'
import type { ElectronAPI } from '@mattverse/shared'
import LOGO from '@mattverse/shared/assets/images/icons/mattverse/mattverse.png'

// 国际化
const { t } = useI18n()

// 应用信息
const appVersion = ref('1.0.0')
const buildType = ref('Development')
const appName = ref('MattVerse')

// 系统信息
const systemInfo = ref({
  platform: '',
  arch: '',
  nodeVersion: '',
  electronVersion: '',
  chromeVersion: '',
})

// 获取应用配置和系统信息
onMounted(async () => {
  try {
    // 获取应用配置
    const electronAPI = (window as any).electronAPI as ElectronAPI | undefined

    if (electronAPI?.getConfig) {
      const config = await electronAPI.getConfig()
      appName.value = config.appName || '--'
      appVersion.value = config.version || '--'
      logger.info('App config loaded:', config)
    }

    // 获取系统信息
    if (electronAPI) {
      // 直接从 electronAPI 获取系统信息
      const platform = (electronAPI as any).platform || 'Unknown'
      const arch = (electronAPI as any).arch || 'Unknown'
      const versions = (electronAPI as any).versions || {}

      systemInfo.value = {
        platform: getPlatformName(platform),
        arch: arch,
        nodeVersion: versions.node || 'Unknown',
        electronVersion: versions.electron || 'Unknown',
        chromeVersion: versions.chrome || 'Unknown',
      }

      // 创建可序列化的日志数据，避免克隆错误
      const serializableSystemInfo = {
        platform: systemInfo.value.platform,
        arch: systemInfo.value.arch,
        nodeVersion: systemInfo.value.nodeVersion,
        electronVersion: systemInfo.value.electronVersion,
        chromeVersion: systemInfo.value.chromeVersion,
      }

      const serializableVersions = {
        node: versions.node || 'Unknown',
        electron: versions.electron || 'Unknown',
        chrome: versions.chrome || 'Unknown'
      }

      logger.info('System info loaded:', serializableSystemInfo)
      logger.info('Available electronAPI properties:', Object.keys(electronAPI))
      logger.info('Platform info:', platform)
      logger.info('Arch info:', arch)
      logger.info('Versions info:', serializableVersions)
    } else {
      // 降级处理：如果 electronAPI 不可用，使用浏览器 API
      systemInfo.value = {
        platform: navigator.platform || 'Unknown',
        arch: 'Unknown',
        nodeVersion: 'Unknown',
        electronVersion: 'Unknown',
        chromeVersion: 'Unknown',
      }
      logger.warn('ElectronAPI not available, using fallback system info')
    }
  } catch (error) {
    logger.error('Failed to get app config or system info:', error)
    // 设置默认值
    systemInfo.value = {
      platform: 'Unknown',
      arch: 'Unknown',
      nodeVersion: 'Unknown',
      electronVersion: 'Unknown',
      chromeVersion: 'Unknown',
    }
  }
})

// 将平台代码转换为友好的名称
const getPlatformName = (platform: string): string => {
  const platformMap: Record<string, string> = {
    win32: 'Windows',
    darwin: 'macOS',
    linux: 'Linux',
    freebsd: 'FreeBSD',
    openbsd: 'OpenBSD',
    aix: 'AIX',
    sunos: 'SunOS',
  }
  return platformMap[platform] || platform
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 如果图片加载失败，可以设置一个默认图标或隐藏
  img.style.display = 'none'
}

// 检查更新
const checkForUpdates = () => {
  // 实现检查更新逻辑
  logger.info('Checking for updates...')
}

// 打开主页
const openHomepage = () => {
  window.open('https://mattverse.com', '_blank')
}

// 查看许可证
const viewLicense = () => {
  // 实现查看许可证逻辑
  logger.info('Viewing license...')
}

// 打开链接
const openLink = (type: string) => {
  const links = {
    github: 'https://github.com/mattverse',
    docs: 'https://docs.mattverse.com',
    support: 'https://support.mattverse.com',
    feedback: 'https://feedback.mattverse.com',
  }

  const url = links[type as keyof typeof links]
  if (url) {
    window.open(url, '_blank')
  }
}
</script>
